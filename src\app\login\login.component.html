<div class="header"></div>
<div class="container row d-flex justify-content-center">
  <div class="col-lg-5 text-center d-none d-lg-flex align-items-end justify-content-center">
    <img class="mascot-img" src="/assets/Images/Mascot.png" alt="">
  </div>

  <div class="col-lg-5 d-flex flex-column align-items-center">
    <span class="heading">Welcome to admin panel</span>
    <form [formGroup]="loginForm" class="w-100 p-4 mt-5 bg-white rounded shadow">
      <div class="mb-3">
        <label for="mobileNumber" class="form-label">Mobile Number</label>
        <input type="text" id="mobileNumber" formControlName="mobileNumber" class="form-control"
          [ngClass]="{ 'is-invalid': submitted && loginForm.get('mobileNumber')?.invalid }" mask="0000000000"
          placeholder="Enter 10 digit mobile number" />
        <div *ngIf="submitted && loginForm.get('mobileNumber')?.errors" class="invalid-feedback">
          <div *ngIf="loginForm.get('mobileNumber')?.errors?.['required']">Mobile number is required</div>
          <div
            *ngIf="loginForm.get('mobileNumber')?.errors?.['minlength'] || loginForm.get('mobileNumber')?.errors?.['pattern']">
            Enter a valid 10 digit mobile number</div>
        </div>
      </div>

      <div class="mb-3" *ngIf="!otpSent">
        <button type="button" class="btn btn-primary w-100" (click)="sendOtp()">Send OTP</button>
      </div>

      <div class="mb-3" *ngIf="otpSent">
        <label for="otp" class="form-label">Enter OTP</label>
        <input type="text" id="otp" inputmode="numeric" pattern="\d*" maxlength="6" formControlName="otp"
          class="form-control" [ngClass]="{ 'is-invalid': submitted && loginForm.get('otp')?.invalid }"
          (keypress)="allowOnlyDigits($event)" />

        <button type="button" class="btn btn-success w-100 mt-3" (click)="verifyOtp()">Verify OTP</button>

        <button type="button" class="btn btn-link mt-2" [disabled]="resendDisabled" (click)="resendOtp()">
          Resend OTP
          <span *ngIf="resendDisabled"> ({{ counter }}s)</span>
        </button>
      </div>
    </form>

  </div>
</div>