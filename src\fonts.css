@font-face {
  font-family: 'ChildHood';
  src: url('assets/Fonts/Child_Hood.otf') format('opentype');
}

@font-face {
  font-family: 'Montserrat-Black';
  src: url('assets/Fonts/Montserrat-Black.ttf') format('truetype');
}

@font-face {
  font-family: 'Montserrat-Bold';
  src: url('assets/Fonts/Montserrat-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'Montserrat-Regular';
  src: url('assets/Fonts/Montserrat-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Montserrat-SemiBold';
  src: url('assets/Fonts/Montserrat-SemiBold.ttf') format('truetype');
}

/* .title {
  font-family: 'ChildHood', sans-serif !important;
}

.heading {
  font-family: 'Montserrat-Bold', sans-serif !important;
}

.paragraph {
  font-family: 'Montserrat-SemiBold', sans-serif !important;
} */
