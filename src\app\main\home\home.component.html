<div class="library-container">
  <div class="library-header">
    <h1 class="library-main-title">Pediatric Library</h1>
    <p class="library-subtitle">Discover comprehensive medical resources for pediatric care</p>
  </div>

  <div class="section-tabs">
    <button class="tab-btn" [class.active]="activeTab === 'books'" (click)="setActiveTab('books')"
            aria-label="View Books Collection">
      <i class="fas fa-book"></i> Books
    </button>
    <button class="tab-btn" [class.active]="activeTab === 'journals'" (click)="setActiveTab('journals')"
            aria-label="View Journals Collection">
      <i class="fas fa-journal-whills"></i> Journals
    </button>
  </div>

  <!-- Books Section -->
  <div class="section-content" *ngIf="activeTab === 'books'" role="tabpanel" aria-labelledby="books-tab">
    <h2 class="section-title">Books Collection</h2>

    <div class="books-grid" *ngIf="books && books.length > 0; else noBooksFound">
      <article class="book-card" *ngFor="let book of books; trackBy: trackByBookId"
               [attr.aria-label]="'Book: ' + book.title">
        <div class="book-image-container">
          <img [src]="book.imageUrl" [alt]="book.title + ' book cover'" class="book-image"
               (error)="onImageError($event)" loading="lazy">
          <div class="book-badge">Book</div>
        </div>
        <div class="book-details">
          <h3 class="book-title">{{book.title}}</h3>
          <p class="book-author">
            <i class="fas fa-user-edit" aria-hidden="true"></i>
            <span>{{book.author}}</span>
          </p>
          <p class="book-date">
            <i class="far fa-calendar-alt" aria-hidden="true"></i>
            <span>{{formatDate(book.publishDate)}}</span>
          </p>
          <p class="book-description">{{truncateText(book.description, 100)}}</p>
          <button class="read-more-btn" (click)="viewBookDetails(book)"
                  [attr.aria-label]="'Read more about ' + book.title">
            <i class="fas fa-book-open"></i> Read More
          </button>
        </div>
      </article>
    </div>

    <!-- Empty state for books -->
    <ng-template #noBooksFound>
      <div class="empty-state">
        <i class="fas fa-book" aria-hidden="true"></i>
        <h3>No Books Available</h3>
        <p>Check back later for new additions to our collection.</p>
      </div>
    </ng-template>
  </div>

  <!-- Journals Section -->
  <div class="section-content" *ngIf="activeTab === 'journals'" role="tabpanel" aria-labelledby="journals-tab">
    <h2 class="section-title">Journals Collection</h2>

    <div class="journals-grid" *ngIf="journals && journals.length > 0; else noJournalsFound">
      <article class="journal-card" *ngFor="let journal of journals; trackBy: trackByJournalId"
               [attr.aria-label]="'Journal: ' + journal.title">
        <div class="journal-image-container">
          <img [src]="journal.imageUrl" [alt]="journal.title + ' journal cover'" class="journal-image"
               (error)="onImageError($event)" loading="lazy">
          <div class="journal-badge">Vol. {{journal.volume}}</div>
        </div>
        <div class="journal-details">
          <h3 class="journal-title">{{journal.title}}</h3>
          <p class="journal-publisher">
            <i class="fas fa-university" aria-hidden="true"></i>
            <span>{{journal.publisher}}</span>
          </p>
          <p class="journal-date">
            <i class="far fa-calendar-alt" aria-hidden="true"></i>
            <span>{{formatDate(journal.publishDate)}}</span>
          </p>
          <p class="journal-description">{{truncateText(journal.description, 100)}}</p>
          <button class="read-more-btn" (click)="viewJournalDetails(journal)"
                  [attr.aria-label]="'View details of ' + journal.title">
            <i class="fas fa-eye"></i> View Journal
          </button>
        </div>
      </article>
    </div>

    <!-- Empty state for journals -->
    <ng-template #noJournalsFound>
      <div class="empty-state">
        <i class="fas fa-journal-whills" aria-hidden="true"></i>
        <h3>No Journals Available</h3>
        <p>Check back later for new journal publications.</p>
      </div>
    </ng-template>
  </div>
</div>


