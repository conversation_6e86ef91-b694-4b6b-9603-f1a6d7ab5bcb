<div class="library-container">
  <!-- Header with tabs -->
  <div class="section-tabs">
    <button class="tab-btn" [class.active]="activeTab === 'books'" (click)="setActiveTab('books')">
      <i class="fas fa-book"></i> Books
    </button>
    <button class="tab-btn" [class.active]="activeTab === 'journals'" (click)="setActiveTab('journals')">
      <i class="fas fa-journal-whills"></i> Journals
    </button>
  </div>

  <!-- Books Section -->
  <div class="section-content" *ngIf="activeTab === 'books'">
    <h2 class="section-title">Books Collection</h2>
    
    <div class="books-grid">
      <div class="book-card" *ngFor="let book of books">
        <div class="book-image-container">
          <img [src]="book.imageUrl" [alt]="book.title" class="book-image">
        </div>
        <div class="book-details">
          <h3 class="book-title">{{book.title}}</h3>
          <p class="book-author"><i class="fas fa-user-edit"></i> {{book.author}}</p>
          <p class="book-date"><i class="far fa-calendar-alt"></i> {{book.publishDate | date:'mediumDate'}}</p>
          <p class="book-description">{{book.description | slice:0:100}}{{book.description.length > 100 ? '...' : ''}}</p>
          <button class="read-more-btn">Read More</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Journals Section -->
  <div class="section-content" *ngIf="activeTab === 'journals'">
    <h2 class="section-title">Journals Collection</h2>
    
    <div class="journals-grid">
      <div class="journal-card" *ngFor="let journal of journals">
        <div class="journal-image-container">
          <img [src]="journal.imageUrl" [alt]="journal.title" class="journal-image">
          <div class="journal-badge">Vol. {{journal.volume}}</div>
        </div>
        <div class="journal-details">
          <h3 class="journal-title">{{journal.title}}</h3>
          <p class="journal-publisher"><i class="fas fa-university"></i> {{journal.publisher}}</p>
          <p class="journal-date"><i class="far fa-calendar-alt"></i> {{journal.publishDate | date:'mediumDate'}}</p>
          <p class="journal-description">{{journal.description | slice:0:100}}{{journal.description.length > 100 ? '...' : ''}}</p>
          <button class="read-more-btn">View Journal</button>
        </div>
      </div>
    </div>
  </div>
</div>


