import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { DataTransferServiceService } from '../services/data-transfer-service.service';
import { Router } from '@angular/router';
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  submitted = false;
  otpSent = false;
  resendDisabled = true;
  counter = 60;
  intervalId: any;
  otpToken: any;

  constructor(private fb: FormBuilder, private toastr: ToastrService,
    private dataTransferService: DataTransferServiceService,
    private router:Router

  ) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      mobileNumber: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      otp: ['', []] // validators added dynamically
    });
  }

  sendOtp(isResend: boolean = false) {
    // this.submitted = true;

    // if (this.loginForm.get('mobileNumber')?.invalid) {
    //   this.toastr.error('Enter a valid mobile number to send OTP', 'Validation Error');
    //   return;
    // }

    // const payload = {
    //   userMobileNumber: this.loginForm.get('mobileNumber')?.value
    // };

    // this.dataTransferService.emailVerification(payload).subscribe({
    //   next: (res: any) => {
    //     if (res.statusCode === 200) {
    //       this.toastr.success(isResend ? 'OTP resent to your mobile' : 'OTP sent to your mobile', 'Success');
    //       this.otpSent = true;
    //       this.otpToken = res.data.token;

    //       this.loginForm.get('otp')?.reset();
    //       this.loginForm.get('otp')?.setValidators([Validators.required, Validators.minLength(6)]);
    //       this.loginForm.get('otp')?.updateValueAndValidity();

    //       this.startResendTimer();
    //     } else {
    //       this.toastr.error('Failed to send OTP. Please try again.', 'Error');
    //     }
    //   },
    //   error: (err) => {
    //     this.toastr.error('Failed to send OTP. Please try again.', 'Error');
    //     console.error('OTP Send Error:', err);
    //   }
    // });
            this.otpSent = true;

    
  }
  


  verifyOtp() {
    // debugger;
    // this.submitted = true;

    // if (this.loginForm.get('otp')?.invalid) {
    //   this.toastr.error('Please enter a valid OTP', 'Validation Error');
    //   return;
    // }

    // const payload = {
    //   userMobileNumber: this.loginForm.get('mobileNumber')?.value,
    //   token: this.otpToken,  
    //   passcode: this.loginForm.get('otp')?.value,
    //   ipAddress: 'Unknown',  
    //   location: 'Unknown'   
    // };

    // this.dataTransferService.passCodeVerify(payload).subscribe({
    //   next: (res: any) => {
    //     if (res.statusCode === 200) {
    //       this.toastr.success('OTP verified, login successful', 'Welcome');
    //       console.log('Login successful', res);
    //       localStorage.setItem('loginUserId',res.data.userId);
    //       localStorage.setItem('token',res.data.token);
    //       localStorage.setItem('loginUserEmailId',res.data.userEmailId);
    //       this.router.navigate([`/dashboard`]);
    //     } else {
    //       this.toastr.error('OTP verification failed', 'Error');
    //     }
    //   },
    //   error: (err) => {
    //     this.toastr.error('Error verifying OTP. Please try again.', 'Error');
    //     console.error('OTP Verification Error:', err);
    //   }
    // });

    localStorage.setItem('token','eyjgvgsbnacicusdkjadsyuwsedryftgyhukbawsetdryftuygdxfhcgvrtjfh');
    this.router.navigate([`/home`]);

  }
  
  
  

  resendOtp() {
    this.sendOtp(true);  
  }
  
  startResendTimer() {
    this.resendDisabled = true;
    this.counter = 60;
    if (this.intervalId) clearInterval(this.intervalId);
    this.intervalId = setInterval(() => {
      this.counter--;
      if (this.counter <= 0) {
        clearInterval(this.intervalId);
        this.resendDisabled = false;
      }
    }, 1000);
  }


  allowOnlyDigits(event: KeyboardEvent) {
    const charCode = event.key.charCodeAt(0);
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  }
  
  

}
