{"name": "pediatric-library", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^13.0.3", "@angular/common": "~13.0.0", "@angular/compiler": "~13.0.0", "@angular/core": "~13.0.0", "@angular/forms": "~13.0.0", "@angular/platform-browser": "~13.0.0", "@angular/platform-browser-dynamic": "~13.0.0", "@angular/router": "~13.0.0", "@fortawesome/fontawesome-free": "^6.7.2", "apexcharts": "^4.5.0", "bootstrap": "^5.3.3", "chart.js": "^3.7.1", "file-saver": "^2.0.5", "ng-apexcharts": "^1.5.0", "ng2-charts": "^3.0.0", "ngx-mask": "^13.2.2", "ngx-pagination": "^6.0.3", "ngx-quill": "^13.3.0", "ngx-toastr": "^14.3.0", "ngx-trim-directive": "^3.0.1", "quill": "^1.3.7", "rxjs": "~7.4.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.0.4", "@angular/cli": "~13.0.4", "@angular/compiler-cli": "~13.0.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.4.3"}}