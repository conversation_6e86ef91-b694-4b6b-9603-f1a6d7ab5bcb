import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { DataTransferServiceService } from '../services/data-transfer-service.service';
import { ToastrService } from 'ngx-toastr';
@Component({
  selector: 'app-home',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.css'],
})
export class MainComponent implements OnInit {
  activeComponent: string = '';
  isSidebarOpen: boolean = false;
  sectionId: any;
  SectionData: any;
  postTitle: string = '';
  postDescription: string = '';
  postImage: File | null = null;
  postURL: string = '';
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private dataTransferService: DataTransferServiceService,
    private toastr: ToastrService
  ) {

  
  }

  ngOnInit(): void {
    const token = sessionStorage.getItem('token');
  if(!token){
    this.router.navigate(['/login']);
  }
  }

  showComponent(component: string) {
    this.activeComponent = component;
  }

  toggleSidebar(): void {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  logout() {
    this.router.navigate([`/login`]);
    sessionStorage.clear();
  }

  showModal() {
    const modal = document.getElementById('logOutModal');
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal() {
    const modal = document.getElementById('logOutModal');
    if (modal != null) {
      modal.style.display = 'none';
    }
  }

 
}
