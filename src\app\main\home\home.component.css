/* Library container */
.library-container {  padding: 2rem;
  background-color: #f8f9fa;  min-height: 100vh;
}
/* Tabs styling */.section-tabs {
  display: flex;  margin-bottom: 2rem;
  border-bottom: 1px solid #e0e0e0;  padding-bottom: 0.5rem;
}
.tab-btn {  background: none;
  border: none;  padding: 0.75rem 1.5rem;
  margin-right: 1rem;  font-size: 1.1rem;
  color: #555;  cursor: pointer;
  transition: all 0.3s ease;  border-radius: 30px;
}
.tab-btn:hover {  color: #269acd;
}
.tab-btn.active {  background-color: #269acd;
  color: white;  font-weight: 600;
  box-shadow: 0 4px 8px rgba(38, 154, 205, 0.2);}
.tab-btn i {
  margin-right: 8px;}
/* Section title */
.section-title {  color: #269acd;
  font-size: 2rem;  margin-bottom: 2rem;
  font-weight: 700;  position: relative;
  padding-bottom: 0.5rem;}
.section-title:after {
  content: '';  position: absolute;
  bottom: 0;  left: 0;
  width: 80px;  height: 4px;
  background-color: #269acd;  border-radius: 2px;
}
/* Books grid */.books-grid, .journals-grid {
  display: grid;  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;}
/* Book card styling */
.book-card, .journal-card {  background-color: white;
  border-radius: 12px;  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;  flex-direction: column;
}
.book-card:hover, .journal-card:hover {  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);}
.book-image-container, .journal-image-container {
  height: 250px;  overflow: hidden;
  position: relative;}
.book-image, .journal-image {
  width: 100%;  height: 100%;
  object-fit: cover;  transition: transform 0.5s ease;
}
.book-card:hover .book-image, .journal-card:hover .journal-image {  transform: scale(1.05);
}
.journal-badge {  position: absolute;
  top: 15px;  right: 15px;
  background-color: #269acd;  color: white;
  padding: 5px 10px;  border-radius: 20px;
  font-size: 0.8rem;  font-weight: 600;
}
.book-details, .journal-details {  padding: 1.5rem;
  flex-grow: 1;  display: flex;
  flex-direction: column;}
.book-title, .journal-title {
  font-size: 1.3rem;  color: #333;
  margin-bottom: 0.8rem;  font-weight: 600;
}
.book-author, .book-date, .journal-publisher, .journal-date {  font-size: 0.9rem;
  color: #666;  margin-bottom: 0.5rem;
}
.book-author i, .book-date i, .journal-publisher i, .journal-date i {  width: 20px;
  color: #269acd;}
.book-description, .journal-description {
  margin: 1rem 0;  color: #555;
  line-height: 1.5;  flex-grow: 1;
}
.read-more-btn {  align-self: flex-start;
  background-color: #269acd;  color: white;
  border: none;  padding: 0.6rem 1.2rem;
  border-radius: 30px;  cursor: pointer;
  font-weight: 600;  transition: all 0.3s ease;
  margin-top: auto;}
.read-more-btn:hover {
  background-color: #1c7ba5;  box-shadow: 0 4px 8px rgba(38, 154, 205, 0.3);
}
/* Responsive adjustments */@media (max-width: 768px) {
  .books-grid, .journals-grid {    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }  
  .library-container {    padding: 1rem;
  }
}
























































































