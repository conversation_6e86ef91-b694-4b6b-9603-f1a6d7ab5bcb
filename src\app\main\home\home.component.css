.library-container {
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.library-header {
  text-align: center;
  margin-bottom: 3rem;
}

.library-main-title {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.library-subtitle {
  font-size: 1.2rem;
  color: #6c757d;
  font-weight: 300;
  margin-bottom: 2rem;
}

.section-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  background: white;
  border-radius: 50px;
  padding: 0.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  gap: 0.5rem;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 40px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  flex: 1;
}

.tab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
  border-radius: 40px;
}

.tab-btn:hover::before {
  left: 0;
}

.tab-btn:hover {
  color: white;
  transform: translateY(-2px);
}

.tab-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  transform: translateY(-2px);
}

.tab-btn.active::before {
  left: 0;
}

.tab-btn i {
  margin-right: 8px;
  font-size: 1.2rem;
}

.section-title {
  color: #2d3748;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  font-weight: 700;
  position: relative;
  text-align: center;
  padding-bottom: 1rem;
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.books-grid, .journals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2.5rem;
  padding: 0 1rem;
}

/* Card styling */
.book-card, .journal-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.book-card::before, .journal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  border-radius: 20px;
}

.book-card:hover::before, .journal-card:hover::before {
  opacity: 1;
}

.book-card:hover, .journal-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.book-image-container, .journal-image-container {
  height: 280px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.book-image, .journal-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(0.9);
}

.book-card:hover .book-image, .journal-card:hover .journal-image {
  transform: scale(1.08);
  filter: brightness(1);
}

.journal-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  backdrop-filter: blur(10px);
}

.book-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}
/* Card details section */
.book-details, .journal-details {
  padding: 2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.book-title, .journal-title {
  font-size: 1.5rem;
  color: #2d3748;
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.book-card:hover .book-title, .journal-card:hover .journal-title {
  color: #667eea;
}

.book-author, .book-date, .journal-publisher, .journal-date {
  font-size: 0.95rem;
  color: #718096;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.book-author i, .book-date i, .journal-publisher i, .journal-date i {
  width: 24px;
  color: #667eea;
  margin-right: 8px;
  font-size: 1rem;
}

.book-description, .journal-description {
  margin: 1.5rem 0;
  color: #4a5568;
  line-height: 1.7;
  flex-grow: 1;
  font-size: 0.95rem;
}

.read-more-btn {
  align-self: flex-start;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: auto;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.read-more-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.read-more-btn:hover::before {
  left: 0;
}

.read-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.read-more-btn:active {
  transform: translateY(0);
}
/* Loading animation */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #718096;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: #cbd5e0;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #4a5568;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .books-grid, .journals-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .library-container {
    padding: 1rem;
  }

  .library-main-title {
    font-size: 2.5rem;
  }

  .section-tabs {
    flex-direction: column;
    max-width: 300px;
    gap: 0.5rem;
  }

  .tab-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .books-grid, .journals-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .book-details, .journal-details {
    padding: 1.5rem;
  }

  .book-image-container, .journal-image-container {
    height: 220px;
  }
}

@media (max-width: 480px) {
  .library-main-title {
    font-size: 2rem;
  }

  .library-subtitle {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .book-title, .journal-title {
    font-size: 1.3rem;
  }

  .book-details, .journal-details {
    padding: 1.2rem;
  }

  .read-more-btn {
    padding: 0.7rem 1.5rem;
    font-size: 0.85rem;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.tab-btn:focus,
.read-more-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .library-container {
    background: white;
  }

  .book-card, .journal-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }
}
























































































