import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MainRoutingModule } from './main-routing.module';
import { MainComponent } from './main.component';
import { HomeComponent } from './home/<USER>';
import { SafeUrlPipe } from '../pipes/safe-url.pipe';
import { NgxPaginationModule } from 'ngx-pagination';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { NgChartsModule } from 'ng2-charts';



@NgModule({
  declarations: [
    MainComponent,
    HomeComponent,
    SafeUrlPipe,
  ],
  imports: [
    CommonModule,
    MainRoutingModule,
    NgxPaginationModule,
    FormsModule,
    ReactiveFormsModule,
    NgChartsModule,
  ]
})
export class MainModule { }
