import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {
  activeTab: string = 'books';
  
  books: any[] = [
    {
      id: 1,
      title: 'Pediatric Fundamentals',
      author: 'Dr. <PERSON>',
      publishDate: new Date('2022-05-15'),
      description: 'A comprehensive guide to pediatric care fundamentals for medical professionals and students.',
      imageUrl: 'assets/Images/book1.jpg'
    },
    {
      id: 2,
      title: 'Child Development & Growth',
      author: 'Dr. <PERSON>',
      publishDate: new Date('2021-11-03'),
      description: 'Explores the physical and cognitive development stages in children from infancy to adolescence.',
      imageUrl: 'assets/Images/book2.jpg'
    },
    {
      id: 3,
      title: 'Pediatric Nutrition Guide',
      author: 'Dr. <PERSON>',
      publishDate: new Date('2023-01-22'),
      description: 'Essential nutrition guidelines for children of all ages, with focus on healthy development and growth.',
      imageUrl: 'assets/Images/book3.jpg'
    },
    {
      id: 4,
      title: 'Common Childhood Illnesses',
      author: 'Dr. <PERSON>',
      publishDate: new Date('2022-08-10'),
      description: 'A practical handbook for diagnosing and treating common pediatric conditions and illnesses.',
      imageUrl: 'assets/Images/book4.jpg'
    }
  ];

  journals: any[] = [
    {
      id: 1,
      title: 'Journal of Pediatric Research',
      publisher: 'International Pediatric Association',
      volume: 42,
      publishDate: new Date('2023-03-15'),
      description: 'Latest research findings in pediatric medicine, including clinical studies and treatment advances.',
      imageUrl: 'assets/Images/journal1.jpg'
    },
    {
      id: 2,
      title: 'Pediatric Infectious Diseases',
      publisher: 'Global Health Institute',
      volume: 28,
      publishDate: new Date('2022-12-05'),
      description: 'Focused on infectious diseases affecting children, prevention strategies, and treatment protocols.',
      imageUrl: 'assets/Images/journal2.jpg'
    },
    {
      id: 3,
      title: 'Child Development Quarterly',
      publisher: 'Child Psychology Foundation',
      volume: 15,
      publishDate: new Date('2023-02-18'),
      description: 'Research on psychological and developmental aspects of childhood and adolescence.',
      imageUrl: 'assets/Images/journal3.jpg'
    },
    {
      id: 4,
      title: 'Pediatric Critical Care',
      publisher: 'Medical Sciences Academy',
      volume: 33,
      publishDate: new Date('2022-09-30'),
      description: 'Advanced research and case studies in pediatric emergency and critical care medicine.',
      imageUrl: 'assets/Images/journal4.jpg'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // You can fetch real data from your service here
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
}

