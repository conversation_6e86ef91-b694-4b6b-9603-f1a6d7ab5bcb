import { Component, OnInit } from '@angular/core';

interface Book {
  id: number;
  title: string;
  author: string;
  publishDate: Date;
  description: string;
  imageUrl: string;
}

interface Journal {
  id: number;
  title: string;
  publisher: string;
  volume: number;
  publishDate: Date;
  description: string;
  imageUrl: string;
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {
  activeTab: string = 'books';
  
  books: Book[] = [
    {
      id: 1,
      title: 'Pediatric Fundamentals',
      author: 'Dr. <PERSON>',
      publishDate: new Date('2022-05-15'),
      description: 'A comprehensive guide to pediatric care fundamentals for medical professionals and students.',
      imageUrl: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=600&fit=crop&crop=center'
    },
    {
      id: 2,
      title: 'Child Development & Growth',
      author: 'Dr. <PERSON>',
      publishDate: new Date('2021-11-03'),
      description: 'Explores the physical and cognitive development stages in children from infancy to adolescence.',
      imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop&crop=center'
    },
    {
      id: 3,
      title: 'Pediatric Nutrition Guide',
      author: 'Dr. Emily Rodriguez',
      publishDate: new Date('2023-01-22'),
      description: 'Essential nutrition guidelines for children of all ages, with focus on healthy development and growth.',
      imageUrl: 'https://images.unsplash.com/photo-**********-ca5e3f4abd8c?w=400&h=600&fit=crop&crop=center'
    },
    {
      id: 4,
      title: 'Common Childhood Illnesses',
      author: 'Dr. James Wilson',
      publishDate: new Date('2022-08-10'),
      description: 'A practical handbook for diagnosing and treating common pediatric conditions and illnesses.',
      imageUrl: 'https://images.unsplash.com/photo-1532012197267-da84d127e765?w=400&h=600&fit=crop&crop=center'
    }
  ];

  journals: Journal[] = [
    {
      id: 1,
      title: 'Journal of Pediatric Research',
      publisher: 'International Pediatric Association',
      volume: 42,
      publishDate: new Date('2023-03-15'),
      description: 'Latest research findings in pediatric medicine, including clinical studies and treatment advances.',
      imageUrl: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=600&fit=crop&crop=center'
    },
    {
      id: 2,
      title: 'Pediatric Infectious Diseases',
      publisher: 'Global Health Institute',
      volume: 28,
      publishDate: new Date('2022-12-05'),
      description: 'Focused on infectious diseases affecting children, prevention strategies, and treatment protocols.',
      imageUrl: 'https://images.unsplash.com/photo-1505664194779-8beaceb93744?w=400&h=600&fit=crop&crop=center'
    },
    {
      id: 3,
      title: 'Child Development Quarterly',
      publisher: 'Child Psychology Foundation',
      volume: 15,
      publishDate: new Date('2023-02-18'),
      description: 'Research on psychological and developmental aspects of childhood and adolescence.',
      imageUrl: 'https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?w=400&h=600&fit=crop&crop=center'
    },
    {
      id: 4,
      title: 'Pediatric Critical Care',
      publisher: 'Medical Sciences Academy',
      volume: 33,
      publishDate: new Date('2022-09-30'),
      description: 'Advanced research and case studies in pediatric emergency and critical care medicine.',
      imageUrl: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=600&fit=crop&crop=center'
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // You can fetch real data from your service here
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  // Utility methods for template
  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength) + '...';
  }

  onImageError(event: any): void {
    // Set a default placeholder image when image fails to load
    event.target.src = 'https://via.placeholder.com/400x600/667eea/ffffff?text=No+Image';
  }

  // Track by functions for better performance
  trackByBookId(_index: number, book: Book): number {
    return book.id;
  }

  trackByJournalId(_index: number, journal: Journal): number {
    return journal.id;
  }

  // Action methods
  viewBookDetails(book: Book): void {
    console.log('Viewing book details:', book);
    // Implement navigation to book details page
    // this.router.navigate(['/book', book.id]);
  }

  viewJournalDetails(journal: Journal): void {
    console.log('Viewing journal details:', journal);
    // Implement navigation to journal details page
    // this.router.navigate(['/journal', journal.id]);
  }
}

