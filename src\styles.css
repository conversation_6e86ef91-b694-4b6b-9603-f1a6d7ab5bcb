body {
  position: relative;
  font-family: 'Montserrat-Regular', sans-serif;
  z-index: 0;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/assets/Images/White_and_blue_print_cover.png') no-repeat center center;
  background-size: cover;
  opacity: 0.2;
  z-index: -1;
  pointer-events: none;
}

body.no-bg::before {
  background: black;
}
/* 
@font-face {
  font-family: 'ChildHood';
  src: url('assets/Fonts/Child_Hood.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}
  */
.container{
  height: 90vh;
}

.header{
  height: 10vh;
}

.heading{
  font-size: 32px;
  font-weight: bold;
  text-transform: uppercase;
  color: #269acd;
  text-align: center;
}

.heading3{
  font-size: 18px;
  font-weight: 400;

}

@media screen and (max-width: 768px) {
  .mt-small-2{
    margin-top: 16px !important;
  }
  .mb-small-2{
    margin-bottom: 16px !important;
  }
}


.required::after {
  content: " *";
  color: red;
  font-weight: bold;
}


