import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
@Injectable({
  providedIn: 'root'
})
export class DataTransferServiceService {

  constructor(
    private http: HttpClient,
  ) { }


  signin(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}user/adminLogin`, postData);
  }

  emailVerification(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}auth/email-verification`, postData);
  }


  passCodeVerify(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}auth/pass-code-verify`, postData);
  }

  getAllBooks(params: any) {
    return this.http.get(`${environment.apiBaseUrl}ebook/getAll`, {
      params: {
        limit: params.limit,
        offset: params.offset,
        categoryId: params.categoryId || '',
        search: params.search || ''
      }
    });
  }

  addBook(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}ebook/add`, postData);
  }


  updateBook(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}ebook/update`, postData);
  }


  deleteBook(id: any) {
    return this.http.delete(`${environment.apiBaseUrl}ebook/softDelete`, { params: { ebookId: id } });
  }

  uploadImages(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}ebook-content/imgUpload`, postData);
  }

  updateImage(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}ebook-content/update`, postData);
  }


  getEbookImagesById(id: any) {
    return this.http.get(`${environment.apiBaseUrl}ebook-content/byEbook/${id}`);
  }

  deleteBookImage(id: any) {
    return this.http.delete(`${environment.apiBaseUrl}ebook-content/soft-delete/${id}`);
  }

  getAllDataByCategory(param: any) {
    return this.http.get(`${environment.apiBaseUrl}webEbook/category`, {
      params: {
        caTitle: param.caTitle || '',
        caType: param.caType || ''
      }
    });
  }

  getAllCategories(params: any) {
    return this.http.get(`${environment.apiBaseUrl}categories/getAll`, {
      params: {
        limit: params.limit,
        offset: params.offset,
        search: params.search || ''
      }
    });
  }

  createCategory(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}categories/add`, postData);
  }


  updateCategory(id: any, postData: any) {
    return this.http.put(`${environment.apiBaseUrl}categories/${id}`, postData);
  }

  deleteCategory(id: any) {
    return this.http.delete(`${environment.apiBaseUrl}categories/${id}`);
  }

  getAllVideos(params: any) {
    return this.http.get(`${environment.apiBaseUrl}ebook/getAll`, {
      params: {
        limit: params.limit,
        offset: params.offset,
        categoryId: params.categoryId || '',
        search: params.search || ''
      }
    });
  }

  getVideoByCategoryId(id: any) {
    return this.http.get(`${environment.apiBaseUrl}videos/getCategoryById/${id}`,);
  }

  addVideo(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}/videos/add`, postData);
  }
 
  updateVideo(id: any, postData: any) {
    return this.http.put(`${environment.apiBaseUrl}videos/${id}`, postData);
  }

  deleteVideo(id: any) {
    return this.http.delete(`${environment.apiBaseUrl}videos/${id}`);
  }

  getAllDashboardData(){
    return this.http.get(`${environment.apiBaseUrl}total-count`);
  }

}
