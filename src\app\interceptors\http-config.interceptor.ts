import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { catchError, Observable, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';


@Injectable()
export class HttpConfigInterceptor implements HttpInterceptor {

  constructor(private router: Router, private toastr: ToastrService) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    const Authorization=localStorage.getItem('token');
    if (request.url.includes('/login')) {
    return next.handle(request);
    }
    let newHeaders = request.headers;
    if (Authorization) {
      newHeaders = newHeaders.append('Authorization', Authorization);
    } else {
      this.router.navigate(['/login']);
    }

    const authReq = request.clone({ headers: newHeaders });

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 403) {
          localStorage.clear();
          this.router.navigate(['/login']);
          this.toastr.error('Your session has expired. Please log in again.');
        }
        return throwError(() => error);
      })
    );
  }
}
